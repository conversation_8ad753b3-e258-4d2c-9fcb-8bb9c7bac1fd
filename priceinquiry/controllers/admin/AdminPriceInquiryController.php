<?php
/**
 * Admin controller pro modul "Cena na dotaz"
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class AdminPriceInquiryController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'price_inquiry';
        $this->className = 'PriceInquiry';
        $this->lang = false;
        $this->addRowAction('view');
        $this->addRowAction('delete');
        
        $this->bulk_actions = array(
            'delete' => array(
                'text' => $this->l('Delete selected'),
                'confirm' => $this->l('Delete selected items?'),
                'icon' => 'icon-trash'
            )
        );

        $this->fields_list = array(
            'id_price_inquiry' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'date_add' => array(
                'title' => $this->l('Datum'),
                'width' => 'auto',
                'type' => 'datetime'
            ),
            'customer_name' => array(
                'title' => $this->l('Zákazník'),
                'width' => 'auto'
            ),
            'customer_email' => array(
                'title' => $this->l('E-mail'),
                'width' => 'auto'
            ),
            'product_name' => array(
                'title' => $this->l('Produkt'),
                'width' => 'auto'
            ),
            'quantity' => array(
                'title' => $this->l('Množství'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'resolved' => array(
                'title' => $this->l('Vyřešeno'),
                'align' => 'center',
                'active' => 'resolved',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            )
        );

        parent::__construct();
    }

    public function renderList()
    {
        $this->addRowActionSkipList('view', array());
        $this->addRowActionSkipList('delete', array());

        return parent::renderList();
    }

    public function renderView()
    {
        $inquiry = $this->loadObject();
        if (!Validate::isLoadedObject($inquiry)) {
            $this->errors[] = Tools::displayError('Dotaz nebyl nalezen.');
            return false;
        }

        $this->context->smarty->assign(array(
            'inquiry' => $inquiry,
            'back_url' => $this->context->link->getAdminLink('AdminPriceInquiry')
        ));

        return $this->context->smarty->fetch(_PS_MODULE_DIR_.'priceinquiry/views/templates/admin/inquiry_view.tpl');
    }

    public function postProcess()
    {
        if (Tools::isSubmit('submitResolve'.$this->table)) {
            $inquiry = $this->loadObject();
            if (Validate::isLoadedObject($inquiry)) {
                $inquiry->resolved = 1;
                if ($inquiry->update()) {
                    $this->confirmations[] = $this->l('Dotaz byl označen jako vyřešený.');
                } else {
                    $this->errors[] = Tools::displayError('Chyba při označování dotazu.');
                }
            }
        }

        return parent::postProcess();
    }

    protected function loadObject($opt = false)
    {
        $id = (int)Tools::getValue('id_price_inquiry');
        if (!$id) {
            return false;
        }

        $sql = 'SELECT * FROM `'._DB_PREFIX_.'price_inquiry` WHERE `id_price_inquiry` = '.$id;
        $data = Db::getInstance()->getRow($sql);
        
        if (!$data) {
            return false;
        }

        // Vytvoříme objekt s daty
        $obj = new stdClass();
        foreach ($data as $key => $value) {
            $obj->$key = $value;
        }
        
        return $obj;
    }

    public function initToolbar()
    {
        parent::initToolbar();
        
        // Přidáme tlačítko pro konfiguraci
        $this->toolbar_btn['config'] = array(
            'href' => $this->context->link->getAdminLink('AdminModules').'&configure=priceinquiry',
            'desc' => $this->l('Konfigurace modulu')
        );
    }
}
